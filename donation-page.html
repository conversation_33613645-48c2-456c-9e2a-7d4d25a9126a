<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help Us Reach Our Goal - Donation Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .donation-section {
            padding: 40px;
        }

        .progress-container {
            position: relative;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .donation-image {
            position: relative;
            width: 100%;
            height: 400px;
            background-image: url('FImu9GbWYA4gzhX.jfif');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            background-color: #f8f9fa;
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(128, 128, 128, 0.8);
            transition: all 0.8s ease-in-out;
        }

        .progress-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat {
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ee5a24);
            border-radius: 10px;
            transition: width 0.8s ease-in-out;
            width: 0%;
        }

        .donation-amounts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .amount-btn {
            padding: 15px 20px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            transition: all 0.3s ease;
            text-align: center;
        }

        .amount-btn:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .amount-btn.selected {
            background: #3498db;
            color: white;
        }

        .custom-amount {
            margin: 20px 0;
        }

        .custom-amount input {
            width: 100%;
            padding: 15px;
            border: 2px solid #bdc3c7;
            border-radius: 10px;
            font-size: 1.1em;
            transition: border-color 0.3s ease;
        }

        .custom-amount input:focus {
            outline: none;
            border-color: #3498db;
        }

        .donate-btn {
            width: 100%;
            padding: 20px;
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.3em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .donate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(46, 204, 113, 0.3);
        }

        .donate-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .goal-info {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 10px;
            border-left: 5px solid #2ecc71;
        }

        .goal-info h3 {
            color: #27ae60;
            margin-bottom: 10px;
        }

        .supporters {
            text-align: center;
            margin: 20px 0;
            color: #7f8c8d;
        }

        @media (max-width: 600px) {
            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .donation-section {
                padding: 30px 20px;
            }

            .donation-amounts {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Help Us Make a Difference</h1>
            <p>Every donation brings us closer to our goal</p>
        </div>

        <div class="donation-section">
            <div class="progress-container">
                <div class="donation-image">
                    <div class="image-overlay" id="imageOverlay"></div>
                </div>
            </div>

            <div class="progress-stats">
                <div class="stat">
                    <div class="stat-value" id="currentAmount">$0</div>
                    <div class="stat-label">Raised</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="goalAmount">$10,000</div>
                    <div class="stat-label">Goal</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="donorCount">0</div>
                    <div class="stat-label">Supporters</div>
                </div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="goal-info">
                <h3>Our Mission</h3>
                <p>Your generous donation will help us achieve our important goal. Every contribution, no matter the
                    size, makes a meaningful impact in our community.</p>
            </div>

            <div class="donation-amounts">
                <div class="amount-btn" data-amount="25">$25</div>
                <div class="amount-btn" data-amount="50">$50</div>
                <div class="amount-btn" data-amount="100">$100</div>
                <div class="amount-btn" data-amount="250">$250</div>
                <div class="amount-btn" data-amount="500">$500</div>
                <div class="amount-btn" data-amount="1000">$1,000</div>
            </div>

            <div class="custom-amount">
                <input type="number" id="customAmount" placeholder="Enter custom amount ($)" min="1">
            </div>

            <button class="donate-btn" id="donateBtn" disabled>Select Amount to Donate</button>

            <div class="supporters">
                <p>Join <span id="supporterCount">0</span> other supporters in making this possible!</p>
            </div>
        </div>
    </div>

    <script>
        // Donation tracking variables
        let currentTotal = 2500; // Starting amount already raised
        let goalAmount = 10000;
        let donorCount = 47;
        let selectedAmount = 0;

        // DOM elements
        const imageOverlay = document.getElementById('imageOverlay');
        const progressFill = document.getElementById('progressFill');
        const currentAmountEl = document.getElementById('currentAmount');
        const donorCountEl = document.getElementById('donorCount');
        const supporterCountEl = document.getElementById('supporterCount');
        const donateBtn = document.getElementById('donateBtn');
        const customAmountInput = document.getElementById('customAmount');
        const amountBtns = document.querySelectorAll('.amount-btn');

        // Initialize the page
        function init() {
            updateProgress();
            setupEventListeners();
        }

        // Update progress visualization
        function updateProgress() {
            const percentage = Math.min((currentTotal / goalAmount) * 100, 100);

            // Update progress bar
            progressFill.style.width = percentage + '%';

            // Update image overlay (gray overlay reduces as donations increase)
            const overlayOpacity = Math.max(0, 0.8 - (percentage / 100) * 0.8);
            imageOverlay.style.background = `rgba(128, 128, 128, ${overlayOpacity})`;

            // Update stats
            currentAmountEl.textContent = '$' + currentTotal.toLocaleString();
            donorCountEl.textContent = donorCount;
            supporterCountEl.textContent = donorCount;
        }

        // Setup event listeners
        function setupEventListeners() {
            // Amount button clicks
            amountBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    // Remove selected class from all buttons
                    amountBtns.forEach(b => b.classList.remove('selected'));

                    // Add selected class to clicked button
                    btn.classList.add('selected');

                    // Set selected amount
                    selectedAmount = parseInt(btn.dataset.amount);
                    customAmountInput.value = '';

                    // Update donate button
                    updateDonateButton();
                });
            });

            // Custom amount input
            customAmountInput.addEventListener('input', () => {
                // Remove selected class from all buttons
                amountBtns.forEach(btn => btn.classList.remove('selected'));

                // Set selected amount
                selectedAmount = parseInt(customAmountInput.value) || 0;

                // Update donate button
                updateDonateButton();
            });

            // Donate button click
            donateBtn.addEventListener('click', () => {
                if (selectedAmount > 0) {
                    processDonation(selectedAmount);
                }
            });
        }

        // Update donate button state
        function updateDonateButton() {
            if (selectedAmount > 0) {
                donateBtn.disabled = false;
                donateBtn.textContent = `Donate $${selectedAmount.toLocaleString()}`;
            } else {
                donateBtn.disabled = true;
                donateBtn.textContent = 'Select Amount to Donate';
            }
        }

        // Process donation (simulate)
        function processDonation(amount) {
            // Simulate donation processing
            donateBtn.textContent = 'Processing...';
            donateBtn.disabled = true;

            setTimeout(() => {
                // Add donation to total
                currentTotal += amount;
                donorCount += 1;

                // Update progress
                updateProgress();

                // Reset form
                selectedAmount = 0;
                customAmountInput.value = '';
                amountBtns.forEach(btn => btn.classList.remove('selected'));

                // Show success message
                alert(`Thank you for your generous donation of $${amount.toLocaleString()}! 🎉`);

                // Reset button
                updateDonateButton();
            }, 2000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>

</html>